import BaseEventHandler from './BaseEventHandler'
import { resumeAllTasks } from '@/common/oss'
import service from '../../service/service'
import multiCenterService from '../../service/multiCenterService'
import { patientDesensitization, parseImageListToLocal, setIworksInfoToMsg } from '../common_base'

/**
 * 网关事件处理器
 * 处理网关连接、断开、重连等相关事件
 */
class GatewayEventHandler extends BaseEventHandler {
    /**
     * 构造函数
     * @param {Object} vueInstance Vue实例
     * @param {Object} autoLoginManager 自动登录管理器
     * @param {Object} mainScreenManager 主屏幕管理器
     * @param {Object} eventListenerManager 事件监听管理器
     */
    constructor(vueInstance, autoLoginManager, mainScreenManager, eventListenerManager) {
        super(vueInstance, autoLoginManager, mainScreenManager, eventListenerManager)
    }
    /**
     * 初始化网关相关事件
     * @param {Object} controller 控制器
     */
    initEvents(controller) {
        controller.on('gateway_connect', () => {
            this.autoLoginManager.onSocketConnectSuccess()
            this.mainScreenManager.onSocketConnectSuccess()
            this.getAllTags()

            this.getMultiCenterOptionList()
            this.eventListenerManager.otherEventHandler.getDeviceNameById()
            this.getGroupSetList()
            this.getAiAnalyzeTypes()
            this.eventListenerManager.updateLiveCount()
            controller.emit("get_consultation_image_list", {
                start: 0,
                count: this.vm.systemConfig.consultationImageShowNum
            }, this.setConsultationImageList.bind(this))

            controller.emit('get_all_hospital_name', this.setAllHospital.bind(this))
            controller.emit('get_user_info')
            controller.emit('get_version_info')
            controller.emit("init_main_screen_controller_event", controller)

            setTimeout(() => {
                resumeAllTasks()
            }, 2000)

            if (this.vm.user.pacsCid) {
                this.vm.joinAndStartConversation(this.vm.user.pacsCid)
            }

            this.vm.$refs['user_avatar'].ifCreateUserAvatar()
            this.vm.$refs['init_organization'].init()
        })

        controller.on("gateway_error", (data) => {
            this.vm.resetApp()
            this.autoLoginManager.onSocketError(data)
        })

        controller.on("gateway_reconnecting", () => {
            this.mainScreenManager.onSocketReconnecting()
        })

        controller.on("gateway_reconnect_fail", (data) => {
            this.autoLoginManager.onSocketReconnectFail()
            this.mainScreenManager.onSocketReconnectFail(data)
        })

        controller.on("gateway_reconnect", () => {
            this.mainScreenManager.onSocketReconnect()
        })

        controller.on("gateway_disconnect", (data) => {
            this.autoLoginManager.onSocketDisconnect(data)
            this.mainScreenManager.onSocketDisconnect(data)
        })
    }


    getAllTags(){
        service.getAllTags().then((res)=>{
            if (res.data.error_code==0) {
                this.vm.$store.commit('gallery/addTagTopInfo', res.data.data)
            }
        })
    }

    getMultiCenterOptionList(){
        multiCenterService.getMultiCenterAllOptions().then((res)=>{
            if (res.data.error_code==0) {
                this.vm.$store.commit('multicenter/updateMCOptionList',res.data.data);
            }
        })
    }
    getGroupSetList(){
        window.main_screen.getGroupsetList({},(data)=>{
            console.log('getGroupsetList',data)
            if (data.error_code==0) {
                let list=data.data;
                list.forEach(item=>{
                    item.type=this.vm.systemConfig.ConversationConfig.type.GroupSet;
                })
                this.vm.$store.commit('groupset/initGroupsetList',data.data);
            }
        })
        this.getManagerGroupsetList()
    }
    getManagerGroupsetList(){
        window.main_screen.getManagerGroupsetList({},(data)=>{
            console.log(data,'getManagerGroupsetList')
            if (data.error_code==0) {
                let list=data.data;
                list.forEach(item=>{
                    item.type=this.vm.systemConfig.ConversationConfig.type.GroupSet;
                })
                this.vm.$store.commit('groupset/initGroupsetManagerList',data.data);
            }
        })
    }
    getAiAnalyzeTypes(){
        service.getAiAnalyzeTypes().then((res)=>{
            if (res.data.error_code==0) {
                this.vm.$store.commit('aiPresetData/updateAiPresetData',res.data.data)
            }
        })
    }

    setConsultationImageList(is_succ,data){
        console.log('setConsultationImageList',is_succ,data)
        //放置图像列表数据
        if(is_succ){
            patientDesensitization(data.consultation_image_list);
            parseImageListToLocal(data.consultation_image_list,'url')
            if(data.iworks_protocol_list){
                this.eventListenerManager.setGalleryMessageDetail(is_succ,{iworks_protocol_list:data.iworks_protocol_list})
            }
            let consultation_image_list = data.consultation_image_list
            for (let item of  data.consultation_image_list) {
                setIworksInfoToMsg(item);
            }
            if(is_succ != "net_error"){
                this.vm.$store.commit('consultationImageList/initConsultationImages',data)
            }
            this.vm.$store.commit('loadingConfig/updateLoaded',{
                key:'loadedFileList',
                loaded:true
            });
        }else{
            this.vm.$message.error('setConsultationImageList error')
        }
    }

    setAllHospital(is_succ,data){
        console.error('setAllHospital',this)
        if (is_succ) {
            this.vm.$store.commit('dynamicGlobalParams/updateDynamicGlobalParams',{
                hospitals:data
            })
        }
    }
}

export default GatewayEventHandler
