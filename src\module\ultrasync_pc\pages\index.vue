<template>
    <div class="deep_bg_wrap"
        v-loading.fullscreen.lock="loginLoading"
        :element-loading-text="lang.loginLoading">
        <div class="main_viewport">
            <div class="flex_container">
                <main-menu></main-menu>
                <gallery></gallery>
                <report></report>
                <transmit></transmit>
                <div class="network_unavailable" v-show="loadingConfig.networkUnavailable">{{lang.network_unavailable}}</div>
                <audio id="message_notify" src='static/resource_pc/audio/notify.wav'></audio>
                <div class="chat_container">
                    <left-tab></left-tab>
                    <chat-window></chat-window>
                    <router-view></router-view>

                </div>
            </div>
            <span class="web_build_time" v-if="devEnv">buildTime:{{buildTime}}</span>
            <a target="_blank" href="https://beian.miit.gov.cn/#/Integrated/index" class="license" v-if="!globalParams.isCef&&!globalParams.isCE">粤ICP备05083646号-4</a>
        </div>
    </div>
</template>
<script>
import leftTab from '../components/leftTab'
import mainMenu from '../components/mainMenu'
import chatWindow from '../components/chatWindow'
import transmit from '../components/transmit'
import base from '../lib/base'
import groupsetTool from '../lib/groupsetTool'
import sendMessage from '../lib/sendMessage'
import Tool from '@/common/tool.js'
import iworksTool from '../lib/iworksTool';
import report from '../components/report.vue'
import CWorkstationCommunicationMng from '@/common/CommunicationMng/index'

import {
    parseImageListToLocal,

} from '../lib/common_base'
export default {
    name: 'index_pc',
    mixins:[base, sendMessage,iworksTool,groupsetTool],
    components:{
        leftTab,
        chatWindow,
        mainMenu,
        transmit,
        gallery: () => import(/* webpackPrefetch: true */ '../components/gallery.vue'),
        report,
    },
    data(){
        return {
            loadingConfig:this.$store.state.loadingConfig,
            loginLoading:false,
        }
    },
    computed:{
        buildTime() {
            return process.env.VUE_APP_BUILD_TIME;
        },
        devEnv(){
            return this.user.build_version === 'dev'
        }
    },
    beforeCreate(){

    },
    created(){


    },
    mounted(){
        this.$nextTick(()=>{



        })
    },
    methods:{



    }
}
</script>
<style lang="scss">
@import "@/module/ultrasync_pc/style/base.scss";
.deep_bg_wrap{
    background-color:#a9bfbe;
    height:100%;
    position:relative;
    z-index:1;
    word-break: break-all;
    .main_viewport{
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        overflow: hidden;
        background-color:#a9bfbe;
        .flex_container{
            display: flex;
            flex-direction: column;
            min-height: 600px;
            min-width: 1200px;
            height: 100%;
            position:relative;
            overflow:hidden;
            .network_unavailable{
                position: absolute;
                left: 50%;
                top: 100px;
                z-index: 9001;
                transform: translate(-50%);
                background: #f92d2d;
                color: #fff;
                padding: 8px 20px;
                border-radius: 10px;
                box-shadow: 4px 4px 4px rgba(0,0,0,.3);
            }
            .chat_container{
                display: flex;
                min-height: 0;
                user-select:none;
                position: relative;
                z-index: 10;
                flex: 1;
            }
        }
        .license{
            position:absolute;
            right:10px;
            bottom:0;
            font-size:10px;
            color:#000;
            z-index:10;
        }
        .web_build_time{
            position:absolute;
            right:260px;
            top:0;
            font-size:10px;
            color:#000;
        }
    }
}
.offline{
    filter: grayscale(100%);
    -webkit-filter: grayscale(100%);
//     -webkit-transform: translateZ(0);
}
</style>
