import BaseEventHandler from './BaseEventHandler'

/**
 * 直播事件处理器
 * 处理直播相关的事件，如直播开始/停止、录制等
 */
class LiveEventHandler extends BaseEventHandler {
    /**
     * 构造函数
     * @param {Object} vueInstance Vue实例
     * @param {Object} autoLoginManager 自动登录管理器
     * @param {Object} mainScreenManager 主屏幕管理器
     * @param {Object} eventListenerManager 事件监听管理器
     */
    constructor(vueInstance, autoLoginManager, mainScreenManager, eventListenerManager) {
        super(vueInstance, autoLoginManager, mainScreenManager, eventListenerManager)
    }

    /**
     * 初始化直播相关事件
     * @param {Object} controller 控制器
     */
    initEvents(controller) {
        controller.on("notify_agora_live_start", (data) => {
            this.NotifyAgoraLiveStart(data)
        })

        controller.on("notify_agora_live_stop", (data) => {
            this.NotifyAgoraLiveStop(data)
        })

        controller.on("notify_update_recording", (data) => {
            this.NotifyUpdateLiveRecord(data)
        })

    }

    /**
     * 通知直播开始
     * @param {Object} data 直播数据
     */
    NotifyAgoraLiveStart(data) {
        this.vm.$store.commit('liveConference/updateConferenceState', {
            cid: data.groupInfo.id,
            obj: {
                conferenceState: 1,
                senderUserId: data.host_uid
            }
        })

        const info = {
            ...data.groupInfo,
            nickname: data.hostNickname,
            fid: data.host_uid,
            avatar: data.hostAvatar || data.groupInfo.avatar
        }
        this.vm.$set(this.vm, 'livingGroupInfo', info)

        // 需要调用主管理器的排序方法
        console.log('this.eventListenerManager', this.eventListenerManager)
        if (this.eventListenerManager?.debounceSortChatList) {
            this.eventListenerManager.debounceSortChatList()
        }

        if (!data.hasOwnProperty('liveStartTime')) { // 该直播没有曾经开播时间
            this.vm.showLivingNotifyDialog = true
        }
    }

    /**
     * 通知直播停止
     * @param {Object} data 直播数据
     */
    NotifyAgoraLiveStop(data) {
        this.vm.$store.commit('liveConference/updateConferenceState', {
            cid: data.groupInfo.id,
            obj: {
                conferenceState: 0,
                senderUserId: 0
            }
        })
        this.vm.showLivingNotifyDialog = false
    }

    /**
     * 通知更新直播录制
     * @param {Object} data 录制数据
     */
    NotifyUpdateLiveRecord(data) {
        console.log('NotifyUpdateLiveRecord', data)
        this.vm.$store.commit("conversationList/updateChatMessageLiveRecordData", {
            group_id: data.gid,
            resource_id: data.resource_id,
            live_record_data: data.resource.more_details,
            coverUrl: data.coverUrl
        })
        this.vm.$store.commit("consultationImageList/updateConsultationLiveRecordData", {
            group_id: data.gid,
            resource_id: data.resource_id,
            live_record_data: data.resource.more_details,
            coverUrl: data.coverUrl
        })
    }


}

export default LiveEventHandler
